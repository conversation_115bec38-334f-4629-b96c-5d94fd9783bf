<odoo>
    <record id="th_lpm_project_kanban_view" model="ir.ui.view">
        <field name="name">th_lpm_project_kanban_view</field>
        <field name="model">th.project.lpm2</field>
        <field name="arch" type="xml">
            <kanban default_group_by="th_project_status_id" action="th_lpm_action_view_courses" type="object">
                <field name="th_project_status_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="o_kanban_card_header">
                            <div class="o_kanban_card_header_title">
                                <div class="o_kanban_record_title oe_kanban_details">
                                    <strong>
                                        <h3>
                                            <field name="name"/>
                                        </h3>
                                    </strong>
                                </div>
                                <span>Quản lý dự án:
                                    <field name="th_project_manager_id"/>
                                </span>
                                <br/>
                                <button type="object" name="th_lpm_project_config_action" class="btn btn-primary mt8">
                                    <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> dự án
                                </button>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="th_lpm_project_tree_view" model="ir.ui.view">
        <field name="name">th_lpm_project_tree_view</field>
        <field name="model">th.project.lpm2</field>
        <field name="arch" type="xml">
            <tree string="Dự án">
                <field name="th_project_code"/>
                <field name="name" optional="show"/>
                <field name="th_description" optional="show"/>
                <field name="th_course_count" optional="show"/>
                <field name="th_project_manager_id" optional="show"/>
                <field name="th_member_ids" optional="show"/>
                <field name="th_start_date" optional="show"/>
                <field name="th_end_date" optional="show"/>
                <field name="th_department" optional="show"/>
                <field name="th_production_office_id" optional="show"/>
                <field name="th_project_status_id" optional="show"/>
            </tree>
        </field>
    </record>
    <record id="th_lpm_project_form_view" model="ir.ui.view">
        <field name="name">th_lpm_project_form_view</field>
        <field name="model">th.project.lpm2</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="th_project_code" required="1"/>
                            <field name="name" required="1"/>
                            <field name="th_description"/>
                            <field name="th_project_manager_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_course_count"/>
                        </group>
                        <group name='group_right'>
                            <field name="th_start_date"/>
                            <field name="th_end_date"/>
                            <field name="th_department"/>
                            <field name="th_production_office_id" options="{'no_create': True,'no_edit': True, 'no_open':True}" required="1"/>
                            <field name="th_project_status_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                        </group>
                        <notebook>
                            <page string="Nhân sự tham gia" name="th_user">
                                <field name="th_member_ids" mode="kanban" class="w-100">
                                    <kanban create="0">
                                        <field name="id"/>
                                        <field name="name"/>
                                        <field name="email"/>
                                        <field name="avatar_128"/>
                                        <templates>
                                            <t t-name="kanban-box">
                                                <div class="oe_kanban_card oe_kanban_global_click">
                                                    <div class="o_kanban_card_content d-flex">
                                                        <div>
                                                            <img t-att-src="kanban_image('res.users', 'avatar_128', record.id.raw_value)"
                                                                 class="o_kanban_image o_image_64_cover" alt="Avatar"/>
                                                        </div>
                                                        <div class="oe_kanban_details d-flex flex-column ms-3">
                                                            <strong class="o_kanban_record_title oe_partner_heading">
                                                                <field name="name"/>
                                                            </strong>
                                                            <div class="d-flex align-items-baseline text-break">
                                                                <i class="fa fa-envelope me-1" role="img"
                                                                   aria-label="Email" title="Email"/>
                                                                <field name="email"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <a type="delete"
                                                   style="position: absolute; right: 0; padding: 4px; display: inline-block; color: black;"
                                                   class="fa fa-trash">
                                                    <span style="display:none">Delete</span>
                                                </a>
                                            </t>
                                        </templates>
                                    </kanban>
                                </field>
                            </page>
                        </notebook>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="th_lpm_project_form_action" model="ir.actions.act_window">
        <field name="name">Cấu hình dự án</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.project.lpm2</field>
        <field name="view_id" ref="th_lpm_project_form_view"/>
        <field name="view_mode">form</field>
    </record>
    <record id="th_lpm_project_kanban_action" model="ir.actions.act_window">
        <field name="name">Dự án</field>
        <field name="res_model">th.project.lpm2</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('th_lpm_project_kanban_view')}),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_lpm_project_tree_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_lpm_project_form_view')})]"/>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('th_origin_id.th_module_ids.name', '=', 'APM')]</field>
    </record>
</odoo>