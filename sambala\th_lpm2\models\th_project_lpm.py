from odoo import fields, models, api
from odoo.exceptions import ValidationError


class ThProjectLpm(models.Model):
    _name = 'th.project.lpm2'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = 'Dự án sản xuất học liệu LPM2'

    name = fields.Char(string='Tên dự án')
    th_project_code = fields.Char(string='Mã dự án')
    th_description = fields.Text(string='Diễn giải')
    th_course_count = fields.Integer(string='Số lượng khóa học', compute='_compute_course_count')
    th_project_manager_id = fields.Many2one('res.users', string='Quản lý dự án')
    th_member_ids = fields.Many2many('res.users', string='Nhân sự tham gia')
    th_start_date = fields.Date(string="Ngày bắt đầu")
    th_end_date = fields.Date(string="<PERSON><PERSON><PERSON> kết thúc")
    th_department = fields.Char(string='Bộ phận')
    th_production_office_id = fields.Many2one('th.office.production', string='Phòng sản xuất')
    th_project_status_id = fields.Many2one('th.project.status', string='Trạng thái dự án',
                                           group_expand='_read_group_request_status')
    th_course_ids = fields.One2many('th.course.lpm2', 'th_project_id', string='Danh sách khóa học')
    th_origin_id = fields.Many2one('th.origin', string='Trường')

    # các trường này sẽ được sử dụng trong dự án dài hạn
    th_customer_object = fields.Char(string='Đối tượng khách hàng')
    th_major_ids = fields.Many2many('th.major', string='Ngành học')


    # mở view cấu hình dự án
    def th_lpm_project_config_action(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_project_form_action")
        action.update({
            'res_id': self.id,
            'view_mode': 'form',
        })
        return action

    # mở view khóa học ngắn hạn
    def th_lpm_action_view_courses(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_course_action")
        action['domain'] = [('th_project_id', '=', self.id)]
        current_context = action.get('context', {})
        if isinstance(current_context, str):
            current_context = eval(current_context) if current_context else {}
    
        new_context = {'create': 1, 'default_th_project_id': self.id}
        current_context.update(new_context)
        action['context'] = current_context

        return action

    # tính số lượng khóa học trong dự án
    @api.depends('th_course_ids')
    def _compute_course_count(self):
        for rec in self:
            rec.th_course_count = len(rec.th_course_ids)

    # kiểm tra mã và tên dự án có trùng lặp không
    @api.constrains('th_project_code')
    def _check_unique_code_name(self):
        for rec in self:
            domain = [('id', '!=', rec.id),('th_project_code', '=', rec.th_project_code)]
            if self.search_count(domain):
                raise ValidationError('Không được trùng mã dự án!')

    @api.model
    def _read_group_request_status(self, stages, domain, order):
        if self.env.context.get('th_type_status') == 'short_status':
            rec = self.env['th.project.status'].search([('th_type_status', '=', 'short_status')])
        else:
            rec = self.env['th.project.status'].search([('th_type_status', '=', 'long_status')])
        return rec

    # mở view cấu hình dự án
    def th_lpm_long_project_config_action(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_long_project_form_action")
        action.update({
            'res_id': self.id,
            'view_mode': 'form',
        })
        return action

    # mở view học phần dài hạn
    def th_lpm_action_view_long_courses(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_lpm2.th_lpm_long_course_action")
        action['domain'] = [('th_project_id', '=', self.id)]
        action['context'] = {'create': 1, 'default_th_project_id': self.id}
        return action