<odoo>
    <record id="th_course_lpm2_tree_view" model="ir.ui.view">
        <field name="name">th.course.lpm2.tree</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <tree>
                <header>
                    <button name="th_action_update_course_short_wizard"
                            type="object"
                            string="Cập nhật"
                            class="btn-primary"/>
                </header>
                <field name="th_course_code" string="Mã khóa học"/>
                <field name="th_solution_line"/>
                <field name="th_subject"/>
                <field name="th_topic"/>
                <field name="th_project_template_id"/>
                <field name="th_level_production_id"/>
                <field name="th_production_proposed"/>
                <field name="th_lecturer_status"/>
                <field name="th_course_recording"/>
                <field name="th_content_production_status"/>
                <field name="th_editing"/>
                <field name="th_publishing"/>
                <field name="th_content_responsible_id"/>
                <field name="th_recording_responsible_id"/>
                <field name="th_editing_responsible_id"/>
                <field name="th_publishing_responsible_id"/>
            </tree>
        </field>
    </record>

    <record id="th_course_lpm2_form_view" model="ir.ui.view">
        <field name="name">th.course.lpm2.form</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="th_level_production_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable':'1'}"
                           domain="[('th_type_production', '=', 'short_production')]"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_course_code" required="1"/>
                            <field name="th_project_template_id" required="1" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_solution_line" options="{'no_create': True}"/>
                            <field name="th_subject" options="{'no_create': True}"/>
                            <field name="th_topic" options="{'no_create': True}"/>
                            <field name="th_origin_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_type"/>
                            <field name="th_language"/>
                            <field name="th_object_type"/>
                        </group>
                        <group name="group_right">
                            <field name="th_user_id_domain" invisible="1"/>
                            <field name="th_user_id" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_user_id_domain"/>
                            <field name="th_major_ids" widget="many2many_tags" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_production_proposed"/>
                            <field name="th_lecturer_status"/>
                            <field name="th_course_recording"/>
                            <field name="th_content_production_status"/>
                            <field name="th_editing"/>
                            <field name="th_publishing"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Tiến độ sản xuất">
                            <button name="th_action_open_import_production_stage_short" string="Import" type="object" class="btn-primary"/>
                            <button name="th_action_export_data_production_stage_short" string="Export" type="object" class="btn-primary"/>
                            <field name="th_stage_ids">
                                <tree editable="bottom">
                                    <field name="th_stage"/>
                                    <field name="th_plan_month"/>
                                    <field name="th_start_date"/>
                                    <field name="th_plan_finish_date"/>
                                    <field name="th_actual_finish_date"/>
                                    <field name="th_responsible_id_domain" invisible="1"/>
                                    <field name="th_responsible_id" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_responsible_id_domain"/>
                                    <field name="th_status"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Tiêu chuẩn sản xuất">
                            <field name="th_production_standard" required="1"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_lpm_course_search_view" model="ir.ui.view">
        <field name="name">th.course.lpm2.search</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <search>
                <filter string="HB0" name="level_hb0"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB0')]"/>
                <filter string="HB1" name="level_hb1"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB1')]"/>
                <filter string="HB2" name="level_hb2"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB2')]"/>
                <filter string="HB3" name="level_hb3"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB3')]"/>
                <filter string="HB4" name="level_hb4"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB4')]"/>
                <filter string="HB5" name="level_hb5"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB5')]"/>
                <filter string="HB6" name="level_hb6"
                        domain="[('th_level_production_id.th_level_code', '=', 'HB6')]"/>
                <separator/>
                <filter string="Đã có GV" name="lecturer_available"
                        domain="[('th_lecturer_status', '=', 'available')]"/>
                <filter string="Chưa có GV" name="lecturer_not_available"
                        domain="[('th_lecturer_status', '=', 'not_available')]"/>
                <separator/>
                <filter string="Nội dung đã duyệt" name="content_approved"
                        domain="[('th_content_production_status', '=', 'approved')]"/>
                <filter string="Nội dung chưa duyệt" name="content_not_approved"
                        domain="[('th_content_production_status', '=', 'not_approved')]"/>
                <filter string="Nội dung chưa xử lý" name="content_pending"
                        domain="[('th_content_production_status', '=', 'pending')]"/>
                <separator/>
                <filter string="Quay đã duyệt" name="recording_approved"
                        domain="[('th_course_recording', '=', 'approved')]"/>
                <filter string="Quay chưa duyệt" name="recording_not_approved"
                        domain="[('th_course_recording', '=', 'not_approved')]"/>
                <filter string="Quay chưa xử lý" name="recording_pending"
                        domain="[('th_course_recording', '=', 'pending')]"/>
                <separator/>
                <filter string="Biên tập đã duyệt" name="editing_approved" domain="[('th_editing', '=', 'approved')]"/>
                <filter string="Biên tập chưa duyệt" name="editing_not_approved"
                        domain="[('th_editing', '=', 'not_approved')]"/>
                <filter string="Biên tập chưa xử lý" name="editing_pending" domain="[('th_editing', '=', 'pending')]"/>
                <separator/>
                <filter string="Xuất bản hoàn thành" name="publishing_done" domain="[('th_publishing', '=', 'done')]"/>
                <filter string="Xuất bản chưa hoàn thành" name="publishing_not_done"
                        domain="[('th_publishing', '=', 'not_done')]"/>
                <filter string="Xuất bản chưa xử lý" name="publishing_pending"
                        domain="[('th_publishing', '=', 'pending')]"/>
                <field name="th_content_responsible_id" string="Người phụ trách nội dung"/>
                <field name="th_recording_responsible_id" string="Người phụ trách quay"/>
                <field name="th_editing_responsible_id" string="Người phụ trách biên tập"/>
                <field name="th_publishing_responsible_id" string="Người phụ trách xuất bản"/>

                <separator/>
                <!-- Add grouping options -->
                <group expand="0" string="Group By">
                    <filter string="Người phụ trách nội dung"
                            name="group_content_responsible"
                            context="{'group_by': 'th_content_responsible_id'}"/>
                    <filter string="Người phụ trách quay"
                            name="group_recording_responsible"
                            context="{'group_by': 'th_recording_responsible_id'}"/>
                    <filter string="Người phụ trách biên tập"
                            name="group_editing_responsible"
                            context="{'group_by': 'th_editing_responsible_id'}"/>
                    <filter string="Người phụ trách xuất bản"
                            name="group_publishing_responsible"
                            context="{'group_by': 'th_publishing_responsible_id'}"/>
                </group>
                <searchpanel>
                    <field name="th_level_production_id" icon="fa-star" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user" enable_counters="1"/>
                    <field name="th_production_proposed" icon="fa-lightbulb-o" enable_counters="1"/>
                    <field name="th_lecturer_status" icon="fa-phone" enable_counters="1"/>
                    <field name="th_course_recording" icon="fa-microphone" enable_counters="1"/>
                    <field name="th_content_production_status" icon="fa-tasks" enable_counters="1"/>
                    <field name="th_editing" icon="fa-pencil" enable_counters="1"/>
                    <field name="th_publishing" icon="fa-cloud-upload" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="th_lpm_course_action" model="ir.actions.act_window">
        <field name="name">Khóa học</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.lpm2</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_th_type_course': 'short'}</field>
        <field name="target">current</field>
        <field name="search_view_id" ref="th_lpm_course_search_view"/>
    </record>

    <!-- Action với domain filter theo project - sử dụng active_id từ URL -->
    <record id="th_lpm_course_project_action" model="ir.actions.act_window">
        <field name="name">Khóa học theo dự án</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.lpm2</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_project_id', '=', active_id)]</field>
        <field name="context">{'default_th_type_course': 'short', 'default_th_project_id': active_id, 'create': 1}</field>
        <field name="target">current</field>
        <field name="search_view_id" ref="th_lpm_course_search_view"/>
    </record>
</odoo>